// 渲染进程JavaScript - 桌面应用风格
const { ipc<PERSON>enderer } = require('electron');

// 全局变量
let selectedProfiles = new Set();
let allProfiles = [];
let logMessages = [];
let headlessMode = false;
let minimizeMode = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
async function initializeApp() {
    addLogMessage('info', '正在检测配置文件...');
    
    try {
        await loadProfiles();
        addLogMessage('success', `检测完成，共发现 ${allProfiles.length} 个配置文件`);

    } catch (error) {
        addLogMessage('error', `检测配置文件失败: ${error.message}`);
    }
}

// 加载配置文件列表
async function loadProfiles() {
    try {
        // 调用Python脚本获取配置文件
        const result = await ipcRenderer.invoke('get-chrome-profiles');

        if (result.success) {
            allProfiles = result.profiles;
        } else {
            // 如果Python脚本失败，使用默认配置
            allProfiles = [
                { real_name: 'Default', display_name: '1' }
            ];
            addLogMessage('warning', '配置文件检测失败，使用默认配置');
        }

        renderProfiles();
    } catch (error) {
        // 出错时使用默认配置
        allProfiles = [
            { real_name: 'Default', display_name: '1' }
        ];
        addLogMessage('error', `配置文件检测出错: ${error.message}`);
        renderProfiles();
    }
}

// 渲染配置文件列表 - 固定12列网格布局
function renderProfiles() {
    const profileGrid = document.getElementById('profileGrid');
    profileGrid.innerHTML = '';

    // 渲染实际的配置文件
    allProfiles.forEach(profile => {
        const profileItem = document.createElement('div');
        profileItem.className = 'profile-item';
        profileItem.dataset.profile = profile.display_name;

        profileItem.innerHTML = `
            <div class="profile-number">${profile.display_name}</div>
        `;

        // 添加点击事件
        profileItem.onclick = () => toggleProfile(profile.display_name, profileItem);

        profileGrid.appendChild(profileItem);
    });

    // 计算需要多少个占位符来填满12的倍数
    const totalProfiles = allProfiles.length;
    const remainder = totalProfiles % 12;
    if (remainder !== 0) {
        const placeholdersNeeded = 12 - remainder;

        // 添加带边框但无数字的占位选择器
        for (let i = 0; i < placeholdersNeeded; i++) {
            const placeholder = document.createElement('div');
            placeholder.className = 'profile-item placeholder';
            // 占位符不添加任何内容，保持空白但有边框
            profileGrid.appendChild(placeholder);
        }
    }
}

// 切换配置文件选择状态
function toggleProfile(profileName, profileItem) {
    if (selectedProfiles.has(profileName)) {
        selectedProfiles.delete(profileName);
        profileItem.classList.remove('selected');
    } else {
        selectedProfiles.add(profileName);
        profileItem.classList.add('selected');
    }
    updateSelectionCount();
}

// 全选配置文件
function selectAll() {
    selectedProfiles.clear();
    allProfiles.forEach(profile => {
        selectedProfiles.add(profile.display_name);
    });
    updateProfileSelection();
    addLogMessage('info', '已选择所有配置文件');
}

// 取消全选
function deselectAll() {
    selectedProfiles.clear();
    updateProfileSelection();
    addLogMessage('info', '已取消选择所有配置文件');
}

// 刷新配置文件
async function refreshProfiles() {
    addLogMessage('info', '正在刷新配置文件...');
    try {
        await loadProfiles();
        addLogMessage('success', '配置文件刷新完成');
    } catch (error) {
        addLogMessage('error', `刷新配置文件失败: ${error.message}`);
    }
}

// 更新配置文件选择状态显示
function updateProfileSelection() {
    document.querySelectorAll('.profile-item').forEach(item => {
        const profileName = item.dataset.profile;

        if (selectedProfiles.has(profileName)) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
    updateSelectionCount();
}

// 更新选择计数
function updateSelectionCount() {
    const profileCountEl = document.getElementById('profileCount');
    const selectionCountEl = document.getElementById('selectionCount');

    if (profileCountEl) {
        profileCountEl.textContent = `共 ${allProfiles.length} 个`;
    }
    if (selectionCountEl) {
        selectionCountEl.textContent = `已选择: ${selectedProfiles.size}`;
    }
}

// 启动必应奖励 - 调用实际的主进程功能
async function launchBingRewards() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('bing', `开始启动必应奖励，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);

        // 调用主进程的必应奖励功能
        const result = await ipcRenderer.invoke('launch-bing-rewards', profiles);

        if (result.success) {
            addLogMessage('bing', result.message);

            // 10秒后自动读取记录
            addLogMessage('info', '将在10秒后自动读取记录...');
            setTimeout(() => {
                readRecords();
            }, 10000);
        } else {
            addLogMessage('error', result.message);
        }

    } catch (error) {
        addLogMessage('error', `启动失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 读取记录
async function readRecords() {
    showLoading(true);
    addLogMessage('info', '正在读取签到记录...');

    try {
        // 调用Node.js实现读取实际记录
        const result = await ipcRenderer.invoke('read-records');

        if (result.success && result.data) {
            // 在日志中显示记录数据
            addLogMessage('success', result.message);

            // 显示文件信息
            if (result.data.file_name) {
                addLogMessage('info', `数据来源: ${result.data.file_name}`);
            }

            // 在日志中显示账号数据 - 使用表格样式
            addLogMessage('info', `账号数据对比 (${result.data.yesterday_date} vs ${result.data.today_date})`);

            if (result.data.accounts && result.data.accounts.length > 0) {
                // 在日志中显示HTML表格
                addLogTableToLog(result.data);

                addLogMessage('info', `读取完成，共检查 ${result.data.accounts.length} 个账号`);
            } else {
                addLogMessage('warning', '没有找到账号数据');
            }
        } else {
            addLogMessage('error', result.message || '读取记录失败');
            addLogMessage('warning', '请检查RTBS目录是否存在以及是否有今日/昨日的记录文件');
        }

    } catch (error) {
        addLogMessage('error', `读取记录失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 根据状态类型获取日志颜色
function getStatusColor(statusClass) {
    switch (statusClass) {
        case 'increased': return 'success';
        case 'decreased': return 'error';
        case 'unchanged': return 'info';
        case 'max': return 'warning';
        case 'new': return 'info';
        default: return 'info';
    }
}

// 在日志中添加HTML表格
function addLogTableToLog(data) {
    // 创建紧凑的表格HTML - 移除所有多余空白
    let tableHTML = `<div class="compact-table-wrapper"><table class="compact-data-table"><thead><tr><th class="col-index">序号</th><th class="col-account">账号</th><th class="col-today">今日</th><th class="col-yesterday">昨日</th><th class="col-status">签到状态</th></tr></thead><tbody>`;

    // 添加表格行
    data.accounts.forEach(account => {
        // 显示完整账号，不截断
        const accountDisplay = account.account;

        // 处理今日数据显示
        let todayDisplay = account.today_days;
        let todayClass = '';

        // 如果今日没有数据，显示 - 并标记为空白数据
        if (account.today_days === null || account.today_days === undefined || account.today_days === '') {
            todayDisplay = '-';
            todayClass = 'empty-data';
        }

        // 处理昨日数据显示
        let yesterdayDisplay = account.yesterday_days;
        if (account.yesterday_days === null || account.yesterday_days === undefined || account.yesterday_days === '') {
            yesterdayDisplay = '-';
        }

        // 判断签到状态和奖励状态
        let signInStatus = '';
        let statusClass = '';

        if (todayDisplay === '-') {
            // 今日没有数据
            signInStatus = '空白数据';
            statusClass = 'status-empty';
        } else if (account.today_days === 7 && account.yesterday_days === 7) {
            // 7/7 显示领取奖励
            signInStatus = '领取奖励';
            statusClass = 'status-reward';
        } else {
            // 正常签到状态判断：如果状态包含+1则为成功，否则为失败
            const isSuccess = account.status && account.status.includes('+1');
            signInStatus = isSuccess ? '签到成功' : '签到失败';
            statusClass = isSuccess ? 'status-success' : 'status-failed';
        }

        tableHTML += `<tr class="data-row"><td class="col-index">${account.index}</td><td class="col-account">${accountDisplay}</td><td class="col-today ${todayClass}">${todayDisplay}</td><td class="col-yesterday">${yesterdayDisplay}</td><td class="col-status ${statusClass}">${signInStatus}</td></tr>`;
    });

    tableHTML += `</tbody></table></div>`;

    // 添加到日志中
    addLogMessage('table', tableHTML, true);
}



// 哔哩搜索 - 调用实际的主进程功能
async function launchBilibiliSearch() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('bilibili', `开始哔哩搜索，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);
        const result = await ipcRenderer.invoke('launch-bilibili-search', profiles);

        if (result.success) {
            addLogMessage('bilibili', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `哔哩搜索失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 自动模式 - 调用实际的主进程功能
async function autoMode() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('auto', `开始自动模式，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);
        const result = await ipcRenderer.invoke('auto-mode', profiles);

        if (result.success) {
            addLogMessage('auto', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `自动模式失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 账号注册 - 集成RRR项目的自动注册功能
async function launchAccountSignup() {
    // 显示选择对话框
    const choice = await showRegistrationChoice();

    if (choice === 'manual') {
        // 手动注册 - 打开注册页面
        await launchManualSignup();
    } else if (choice === 'auto') {
        // 自动注册 - 使用RRR项目功能
        await launchAutoRegister();
    }
}

// 显示注册方式选择对话框
async function showRegistrationChoice() {
    return new Promise((resolve) => {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.className = 'registration-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>选择注册方式</h3>
                <p>请选择账号注册方式：</p>
                <div class="modal-buttons">
                    <button class="modal-btn manual-btn">手动注册</button>
                    <button class="modal-btn auto-btn">自动注册</button>
                    <button class="modal-btn cancel-btn">取消</button>
                </div>
                <div class="modal-description">
                    <p><strong>手动注册：</strong>打开注册页面，手动填写信息</p>
                    <p><strong>自动注册：</strong>使用AI自动填写并完成注册流程</p>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .registration-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            }
            .modal-content {
                background: white;
                padding: 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                max-width: 400px;
                width: 90%;
            }
            .modal-content h3 {
                margin: 0 0 16px 0;
                color: #333;
                text-align: center;
            }
            .modal-content p {
                margin: 0 0 16px 0;
                color: #666;
                text-align: center;
            }
            .modal-buttons {
                display: flex;
                gap: 12px;
                margin-bottom: 16px;
            }
            .modal-btn {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                transition: all 0.2s ease;
            }
            .manual-btn {
                background: #3b82f6;
                color: white;
            }
            .manual-btn:hover {
                background: #2563eb;
            }
            .auto-btn {
                background: #10b981;
                color: white;
            }
            .auto-btn:hover {
                background: #059669;
            }
            .cancel-btn {
                background: #6b7280;
                color: white;
            }
            .cancel-btn:hover {
                background: #4b5563;
            }
            .modal-description {
                border-top: 1px solid #e5e7eb;
                padding-top: 16px;
            }
            .modal-description p {
                font-size: 12px;
                text-align: left;
                margin: 8px 0;
                color: #6b7280;
            }
        `;
        document.head.appendChild(style);

        // 事件处理
        modal.querySelector('.manual-btn').onclick = () => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
            resolve('manual');
        };

        modal.querySelector('.auto-btn').onclick = () => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
            resolve('auto');
        };

        modal.querySelector('.cancel-btn').onclick = () => {
            document.body.removeChild(modal);
            document.head.removeChild(style);
            resolve('cancel');
        };

        document.body.appendChild(modal);
    });
}

// 手动注册 - 原有功能
async function launchManualSignup() {
    if (selectedProfiles.size === 0) {
        addLogMessage('warning', '请至少选择一个配置文件');
        return;
    }

    showLoading(true);
    addLogMessage('signup', `开始启动账号注册页面，共 ${selectedProfiles.size} 个配置文件`);

    try {
        const profiles = Array.from(selectedProfiles);
        const result = await ipcRenderer.invoke('launch-account-signup', profiles);

        if (result.success) {
            addLogMessage('signup', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `账号注册启动失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 自动注册 - 新功能
async function launchAutoRegister() {
    showLoading(true);
    addLogMessage('auto-register', '开始自动注册账户...');

    try {
        // 检查状态
        const status = await ipcRenderer.invoke('auto-register-status');
        if (status.isRunning) {
            addLogMessage('warning', '自动注册正在运行中，请等待完成');
            return;
        }

        // 执行自动注册
        addLogMessage('auto-register', '正在启动自动注册流程，请稍候...');
        const result = await ipcRenderer.invoke('auto-register-account', {
            botProtectionWait: 60,
            maxCaptchaRetries: 5
        });

        if (result.success) {
            addLogMessage('success', result.message);

            if (result.accounts && result.accounts.length > 0) {
                // 显示注册成功的账户信息
                result.accounts.forEach(account => {
                    addLogMessage('success', `注册成功: ${account.email}:${account.password}`);
                });
            }
        } else {
            addLogMessage('error', result.message);

            // 如果是依赖问题，提供安装选项
            if (result.message.includes('Python') || result.message.includes('依赖')) {
                addLogMessage('info', '尝试安装Python依赖...');
                await installPythonDependencies();
            }
        }
    } catch (error) {
        addLogMessage('error', `自动注册失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 安装Python依赖
async function installPythonDependencies() {
    try {
        addLogMessage('info', '正在安装Python依赖，请稍候...');
        const result = await ipcRenderer.invoke('install-python-dependencies');

        if (result.success) {
            addLogMessage('success', 'Python依赖安装成功');
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `依赖安装失败: ${error.message}`);
    }
}

// 清除缓存 - 调用实际的主进程功能
async function clearTodayCache() {
    showLoading(true);
    addLogMessage('info', '正在清除今日缓存...');

    try {
        const result = await ipcRenderer.invoke('clear-today-cache');

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `清除缓存失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 切换无头模式 - 使用统一的选中效果
async function toggleHeadlessMode() {
    headlessMode = !headlessMode;
    const btn = document.getElementById('headlessBtn');

    try {
        // 调用主进程设置无头模式
        const result = await ipcRenderer.invoke('set-headless-mode', headlessMode);

        if (result.success) {
            if (headlessMode) {
                btn.classList.add('selected');
                addLogMessage('option', 'Headless Mode enabled');
            } else {
                btn.classList.remove('selected');
                addLogMessage('option', 'Headless Mode disabled');
            }
        } else {
            // 如果设置失败，恢复状态
            headlessMode = !headlessMode;
            addLogMessage('error', `设置无头模式失败: ${result.message}`);
        }
    } catch (error) {
        // 如果调用失败，恢复状态
        headlessMode = !headlessMode;
        addLogMessage('error', `设置无头模式失败: ${error.message}`);
    }
}

// 切换最小化模式 - 使用统一的选中效果
async function toggleMinimizeMode() {
    minimizeMode = !minimizeMode;
    const btn = document.getElementById('minimizeBtn');

    try {
        // 调用主进程设置最小化模式
        const result = await ipcRenderer.invoke('set-minimize-mode', minimizeMode);

        if (result.success) {
            if (minimizeMode) {
                btn.classList.add('selected');
                addLogMessage('option', 'Minimize Mode enabled');
            } else {
                btn.classList.remove('selected');
                addLogMessage('option', 'Minimize Mode disabled');
            }
        } else {
            // 如果设置失败，恢复状态
            minimizeMode = !minimizeMode;
            addLogMessage('error', `设置最小化模式失败: ${result.message}`);
        }
    } catch (error) {
        // 如果调用失败，恢复状态
        minimizeMode = !minimizeMode;
        addLogMessage('error', `设置最小化模式失败: ${error.message}`);
    }
}

// 复制日志
function copyLog() {
    try {
        const logContent = document.getElementById('logContent');
        if (!logContent) {
            console.error('找不到日志容器元素');
            addLogMessage('error', '复制失败: 找不到日志容器元素');
            return;
        }

        const logEntries = logContent.querySelectorAll('.log-entry');
        console.log(`找到 ${logEntries.length} 个日志条目`);

        if (logEntries.length === 0) {
            addLogMessage('warning', '没有日志内容可复制');
            return;
        }

        let logText = '';
        logEntries.forEach((entry, index) => {
            const entryText = entry.textContent || entry.innerText || '';
            logText += entryText + '\n';
            console.log(`日志条目 ${index + 1}: ${entryText}`);
        });

        console.log('准备复制的文本:', logText);

        // 使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(logText).then(() => {
                console.log('日志已复制到剪贴板');
                addLogMessage('success', '日志已复制到剪贴板');
            }).catch(err => {
                console.error('Clipboard API 复制失败:', err);
                fallbackCopyTextToClipboard(logText);
            });
        } else {
            console.log('使用降级复制方案');
            fallbackCopyTextToClipboard(logText);
        }
    } catch (error) {
        console.error('复制日志时发生错误:', error);
        addLogMessage('error', `复制失败: ${error.message}`);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            console.log('日志已复制到剪贴板（降级方案）');
            addLogMessage('success', '日志已复制到剪贴板');
        } else {
            console.error('降级复制方案也失败了');
            addLogMessage('error', '复制失败，请手动选择复制');
        }
    } catch (err) {
        console.error('降级复制方案出错:', err);
        addLogMessage('error', `复制失败: ${err.message}`);
    }
}

// 清空日志
function clearLog() {
    logEntries = [];
    logMessages = [];
    renderLogEntries();
    addLogMessage('info', 'Log cleared');
}

// 保存日志
function saveLog() {
    const logText = logMessages.join('\n');
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chrome-launcher-log-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    addLogMessage('info', 'Log saved');
}

// 关闭所有Chrome - 调用实际的主进程功能
async function closeAllChrome() {
    if (!confirm('确定要关闭所有Chrome进程吗？')) {
        return;
    }

    showLoading(true);
    addLogMessage('info', '正在关闭所有Chrome进程...');

    try {
        const result = await ipcRenderer.invoke('close-all-chrome');

        if (result.success) {
            addLogMessage('success', result.message);
        } else {
            addLogMessage('error', result.message);
        }
    } catch (error) {
        addLogMessage('error', `关闭Chrome进程失败: ${error.message}`);
    } finally {
        showLoading(false);
    }
}

// 显示结果 - 确保结果面板正确显示
function displayResults(data) {
    const resultsPanel = document.getElementById('resultsPanel');
    const resultsContent = document.getElementById('resultsContent');

    if (!resultsPanel || !resultsContent) {
        addLogMessage('error', '结果面板元素未找到');
        return;
    }

    let tableHTML = `
        <div style="margin-bottom: 15px;">
            <strong>账号数据对比 (${data.yesterday_date} vs ${data.today_date})</strong>
        </div>
        <table class="results-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>账号</th>
                    <th>天数</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.accounts.forEach(account => {
        tableHTML += `
            <tr>
                <td>${account.index}</td>
                <td style="font-family: monospace; font-size: 11px;">${account.account}</td>
                <td>${account.today_days}天</td>
                <td><span class="status-badge status-${account.status_class}">${account.status}</span></td>
            </tr>
        `;
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    resultsContent.innerHTML = tableHTML;
    resultsPanel.style.display = 'block';
}

// 关闭结果面板
function closeResults() {
    document.getElementById('resultsPanel').style.display = 'none';
}

// 获取日志前缀图标
function getLogPrefix(type) {
    const prefixes = {
        info: '<i class="fas fa-info-circle" style="color: #17a2b8; width: 14px; display: inline-block; margin-right: 6px;"></i>',
        success: '<i class="fas fa-check-circle" style="color: #28a745; width: 14px; display: inline-block; margin-right: 6px;"></i>',
        warning: '<i class="fas fa-exclamation-triangle" style="color: #ffc107; width: 14px; display: inline-block; margin-right: 6px;"></i>',
        error: '<i class="fas fa-times-circle" style="color: #dc3545; width: 14px; display: inline-block; margin-right: 6px;"></i>',
        option: '<i class="fas fa-cog" style="color: #6f42c1; width: 14px; display: inline-block; margin-right: 6px;"></i>'
    };
    return prefixes[type] || prefixes.info;
}

// 添加日志消息
// 全新的日志系统 - 无滚动条，固定高度显示
let logEntries = [];
const MAX_LOG_ENTRIES = 50; // 最大日志条目数

function addLogMessage(type, message, isHTML = false) {
    // 创建日志条目对象
    const logEntry = {
        id: Date.now() + Math.random(),
        type: type,
        message: message,
        isHTML: isHTML
    };

    // 添加到日志数组
    logEntries.push(logEntry);

    // 保持最大条目数限制
    if (logEntries.length > MAX_LOG_ENTRIES) {
        logEntries = logEntries.slice(-MAX_LOG_ENTRIES);
    }

    // 更新显示
    renderLogEntries();

    // 保存到文本日志（用于复制和保存）
    if (isHTML && type === 'table') {
        logMessages.push('[TABLE] 账号数据对比表格');
    } else {
        // 为文本日志使用纯文字前缀
        const textPrefixes = {
            info: '[INFO]',
            success: '[SUCCESS]',
            warning: '[WARNING]',
            error: '[ERROR]',
            option: '[OPTION]'
        };
        const textPrefix = textPrefixes[type] || textPrefixes.info;
        logMessages.push(`${textPrefix} ${message}`);
    }
}

// 渲染日志条目
function renderLogEntries() {
    const logContent = document.getElementById('logContent');
    if (!logContent) return;

    // 清空现有内容
    logContent.innerHTML = '';

    // 渲染所有日志条目
    logEntries.forEach(entry => {
        const logElement = document.createElement('div');
        logElement.className = `log-entry ${entry.type}`;
        logElement.dataset.id = entry.id;

        if (entry.isHTML && entry.type === 'table') {
            // 表格类型直接插入HTML
            logElement.innerHTML = entry.message;
            logElement.style.display = 'block'; // 表格使用block显示
        } else {
            // 普通文本消息 - 使用HTML支持图标
            logElement.innerHTML = `${getLogPrefix(entry.type)}<span style="flex: 1;">${entry.message}</span>`;
        }

        // 将元素添加到容器中
        logContent.appendChild(logElement);
    });

    // 自动滚动到底部
    logContent.scrollTop = logContent.scrollHeight;
}



// 保存日志
async function saveLog() {
    if (logMessages.length === 0) {
        addLogMessage('warning', '没有日志可保存');
        return;
    }
    
    try {
        const result = await ipcRenderer.invoke('show-save-dialog');
        if (!result.canceled) {
            const content = logMessages.join('\n');
            const saveResult = await ipcRenderer.invoke('save-file', result.filePath, content);
            
            if (saveResult.success) {
                addLogMessage('success', `日志已保存到: ${result.filePath}`);
            } else {
                addLogMessage('error', `保存失败: ${saveResult.error}`);
            }
        }
    } catch (error) {
        addLogMessage('error', `保存日志失败: ${error.message}`);
    }
}

// 显示/隐藏加载状态 - 确保加载遮罩正确显示
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    } else {
        console.warn('Loading overlay element not found');
    }
}

// 移除状态栏相关函数

// 监听来自主进程的日志消息
ipcRenderer.on('log-message', (event, message) => {
    addLogMessage('info', message);
});



// 键盘快捷键
document.addEventListener('keydown', (event) => {
    if (event.ctrlKey) {
        switch (event.key) {
            case 'a':
                event.preventDefault();
                selectAll();
                break;
            case 'd':
                event.preventDefault();
                deselectAll();
                break;
            case 's':
                event.preventDefault();
                saveLog();
                break;
            case 'l':
                event.preventDefault();
                clearLog();
                break;
        }
    }
});
