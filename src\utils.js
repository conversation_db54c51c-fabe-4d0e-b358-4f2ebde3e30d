// 工具函数模块 - 集中管理常用的辅助函数
const Config = require('./config');

class Utils {
    /**
     * 创建标准化的任务结果对象
     * @param {boolean} success - 是否成功
     * @param {string} message - 消息
     * @param {Object} data - 额外数据
     * @returns {Object} 标准化结果对象
     */
    static createTaskResult(success, message, data = {}) {
        return {
            success,
            message,
            timestamp: new Date().toISOString(),
            ...data
        };
    }

    /**
     * 创建成功结果
     * @param {string} message - 成功消息
     * @param {Object} data - 额外数据
     * @returns {Object} 成功结果对象
     */
    static createSuccessResult(message, data = {}) {
        return this.createTaskResult(true, message, data);
    }

    /**
     * 创建失败结果
     * @param {string} message - 失败消息
     * @param {Error} error - 错误对象
     * @returns {Object} 失败结果对象
     */
    static createErrorResult(message, error = null) {
        const data = error ? { error: error.message, stack: error.stack } : {};
        return this.createTaskResult(false, message, data);
    }

    /**
     * 验证Chrome启动器是否已初始化
     * @param {Object} chromeLauncher - Chrome启动器实例
     * @returns {Object|null} 如果未初始化返回错误结果，否则返回null
     */
    static validateChromeLauncher(chromeLauncher) {
        if (!chromeLauncher) {
            return this.createErrorResult(Config.ERROR_CODES.CHROME_NOT_INITIALIZED);
        }
        return null;
    }

    /**
     * 验证配置文件列表
     * @param {Array} profiles - 配置文件列表
     * @returns {Object|null} 如果验证失败返回错误结果，否则返回null
     */
    static validateProfiles(profiles) {
        if (!profiles || profiles.length === 0) {
            return this.createErrorResult(Config.ERROR_CODES.NO_PROFILES_SELECTED);
        }
        return null;
    }

    /**
     * 计算任务执行统计
     * @param {Array} results - 任务结果数组
     * @returns {Object} 统计信息
     */
    static calculateTaskStats(results) {
        const successCount = results.filter(r => r.success).length;
        const failCount = results.length - successCount;
        const successRate = results.length > 0 ? (successCount / results.length * 100).toFixed(1) : 0;
        
        return {
            total: results.length,
            successCount,
            failCount,
            successRate: parseFloat(successRate)
        };
    }

    /**
     * 格式化任务完成消息
     * @param {string} taskName - 任务名称
     * @param {Object} stats - 统计信息
     * @returns {string} 格式化的消息
     */
    static formatTaskCompletionMessage(taskName, stats) {
        return `${taskName}完成 (成功: ${stats.successCount}, 失败: ${stats.failCount}, 成功率: ${stats.successRate}%)`;
    }

    /**
     * 延迟执行
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 安全的JSON解析
     * @param {string} jsonString - JSON字符串
     * @param {*} defaultValue - 默认值
     * @returns {*} 解析结果或默认值
     */
    static safeJsonParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            return defaultValue;
        }
    }

    /**
     * 检查文件是否存在
     * @param {string} filePath - 文件路径
     * @returns {boolean} 文件是否存在
     */
    static fileExists(filePath) {
        const fs = require('fs');
        try {
            return fs.existsSync(filePath);
        } catch (error) {
            return false;
        }
    }

    /**
     * 确保目录存在
     * @param {string} dirPath - 目录路径
     * @returns {boolean} 是否成功创建或已存在
     */
    static ensureDirectoryExists(dirPath) {
        const fs = require('fs');
        try {
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 获取今日日期字符串
     * @returns {string} YYYY-MM-DD格式的日期
     */
    static getTodayDateString() {
        return new Date().toISOString().split('T')[0];
    }

    /**
     * 获取昨日日期字符串
     * @returns {string} YYYY-MM-DD格式的日期
     */
    static getYesterdayDateString() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return yesterday.toISOString().split('T')[0];
    }

    /**
     * 清理临时目录
     * @param {string} tempDir - 临时目录路径
     * @param {Function} logCallback - 日志回调函数
     */
    static cleanupTempDirectory(tempDir, logCallback = null) {
        const fs = require('fs');
        if (fs.existsSync(tempDir)) {
            try {
                fs.rmSync(tempDir, { recursive: true, force: true });
                if (logCallback) {
                    logCallback('临时目录清理完成', 'info');
                }
            } catch (cleanupError) {
                if (logCallback) {
                    logCallback(`警告: 清理临时目录失败: ${cleanupError.message}`, 'warning');
                }
            }
        }
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

module.exports = Utils;
